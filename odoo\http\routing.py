# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Route decorators and routing utilities for Odoo HTTP layer.

This module contains the Controller class, route decorator, and related functionality
for handling HTTP routing and controller management.
"""

import collections
import functools
import inspect
import logging

from odoo.tools import unique
from odoo.tools.func import filter_kwargs

_logger = logging.getLogger(__name__)


class Controller:
    """
    Class mixin that provide module controllers the ability to serve
    content over http and to be extended in child modules.

    Each class :ref:`inheriting <python:tut-inheritance>` from
    :class:`~odoo.http.Controller` can use the :func:`~odoo.http.route`:
    decorator to route matching incoming web requests to decorated
    methods.

    Like models, controllers can be extended by other modules. The
    extension mechanism is different because controllers can work in a
    database-free environment and therefore cannot use
    :class:~odoo.api.Registry:.

    To *override* a controller, :ref:`inherit <python:tut-inheritance>`
    from its class, override relevant methods and re-expose them with
    :func:`~odoo.http.route`:. Please note that the decorators of all
    methods are combined, if the overriding method's decorator has no
    argument all previous ones will be kept, any provided argument will
    override previously defined ones.

    .. code-block:

        class GreetingController(odoo.http.Controller):
            @route('/greet', type='http', auth='public')
            def greeting(self):
                return 'Hello'

        class UserGreetingController(GreetingController):
            @route(auth='user')  # override auth, keep path and type
            def greeting(self):
                return super().handler()
    """
    children_classes = collections.defaultdict(list)  # indexed by module

    @classmethod
    def __init_subclass__(cls):
        super().__init_subclass__()
        if Controller in cls.__bases__:
            path = cls.__module__.split('.')
            module = path[2] if path[:2] == ['odoo', 'addons'] else ''
            Controller.children_classes[module].append(cls)


def route(route=None, **routing):
    """
    Decorate a controller method in order to route incoming requests
    matching the given URL and options to the decorated method.

    .. warning::
        It is mandatory to re-decorate any method that is overridden in
        controller extensions but the arguments can be omitted. See
        :class:`~odoo.http.Controller` for more details.

    :param route: The URL pattern to match. Can be a single string or a
        list of strings for multiple patterns.
    :type route: str or List[str]
    :param str type: The type of request, either ``"http"`` for a
        regular HTTP request or ``"json"`` for a JSON-RPC 2 request.
        Default is ``"http"``.
    :param str auth: The authentication method, can be one of the
        following:

        * ``"user"``: The user must be authenticated and the current
          request will be executed using the rights of the user.
        * ``"public"``: The user may or may not be authenticated. If
          they are, the current request will be executed using the
          rights of the user. If they are not, the current request
          will be executed using the shared Public user.
        * ``"none"``: The method is always active, even if there is no
          database. Mainly used by the framework and authentication
          modules. There is no user associated to the request.

        Default is ``"user"``.
    :param List[str] methods: A list of HTTP methods this route applies
        to. If not specified, all methods are allowed.
    :param bool cors: Whether Cross-Origin Resource Sharing (CORS)
        headers should be returned for this route.
    :param bool csrf: Whether Cross-Site Request Forgery (CSRF)
        protection should be enabled for this route. Default is ``True``
        for ``"http"`` routes, ``False`` for ``"json"`` routes.
    :param bool save_session: Whether the session should be saved after
        the request. Default is ``True``.
    :param bool readonly: Whether the route should be executed in
        read-only mode. Default is ``False``.
    :param int max_content_length: Maximum allowed content length for
        the request body. If not specified, uses the default limit.
    :param bool website: Whether this route is website-specific.
        Default is ``False``.

    .. note::
        The ``auth`` parameter is mandatory. While the request may be
        authenticated, the authentication method is set by the route
        decorator and cannot be changed later.

    .. note::
        A request matching a route with ``auth="none"`` will not have
        an ``env`` or ``session``.

    .. note::
        For JSON-RPC 2 requests (``type="json"``), the ``csrf``
        parameter defaults to ``False`` since CSRF protection is not
        needed for JSON-RPC requests.

    .. note::
        The ``readonly`` parameter can be a callable that takes the
        controller instance as argument and returns a boolean.

    .. note::
        The ``max_content_length`` parameter can be a callable that
        takes the controller instance as argument and returns an integer.

    .. note::
        The ``website`` parameter is used by the website module to
        determine if a route should be available on the website.

    .. note::
        The ``cors`` parameter should be a string containing the allowed
        origin for CORS requests, or ``True`` to allow all origins.

    .. note::
        The ``save_session`` parameter can be used to disable session
        saving for performance reasons on routes that don't modify the
        session.

    .. note::
        The ``methods`` parameter should be a list of HTTP methods
        (e.g., ``['GET', 'POST']``). If not specified, all methods are
        allowed.

    .. note::
        The ``route`` parameter can be a single string or a list of
        strings to match multiple URL patterns with the same handler.

    .. note::
        The ``type`` parameter determines how the request is processed:
        ``"http"`` for regular HTTP requests, ``"json"`` for JSON-RPC 2
        requests.

    .. note::
        The ``auth`` parameter determines the authentication level:
        ``"user"`` requires authentication, ``"public"`` allows
        unauthenticated access, ``"none"`` bypasses all authentication
        from the URL parameters (access error or missing error).
    """
    def decorator(endpoint):
        # Import here to avoid circular imports
        from .dispatchers import _dispatchers

        fname = f"<function {endpoint.__module__}.{endpoint.__name__}>"

        # Sanitize the routing
        assert routing.get('type', 'http') in _dispatchers.keys()
        if route:
            routing['routes'] = [route] if isinstance(route, str) else route
        wrong = routing.pop('method', None)
        if wrong is not None:
            _logger.warning("%s defined with invalid routing parameter 'method', assuming 'methods'", fname)
            routing['methods'] = wrong

        @functools.wraps(endpoint)
        def route_wrapper(self, *args, **params):
            params_ok = filter_kwargs(endpoint, params)
            params_ko = set(params) - set(params_ok)
            if params_ko:
                _logger.warning("%s called ignoring args %s", fname, params_ko)

            result = endpoint(self, *args, **params_ok)
            if routing['type'] == 'http':  # _generate_routing_rules() ensures type is set
                # Import here to avoid circular imports
                from .response import Response
                return Response.load(result)
            return result

        route_wrapper.original_routing = routing
        route_wrapper.original_endpoint = endpoint
        return route_wrapper
    return decorator


def _generate_routing_rules(modules, nodb_only, converters=None):
    """
    Two-fold algorithm used to (1) determine which method in the
    controller inheritance tree should bind to what URL with respect to
    the list of installed modules and (2) merge the various @route
    arguments of said method with the @route arguments of the method it
    overrides.
    """
    def is_valid(cls):
        """ Determine if the class is defined in an addon. """
        path = cls.__module__.split('.')
        return path[:2] == ['odoo', 'addons'] and path[2] in modules

    def build_controllers():
        """ Build a list of controller instances for the given modules. """
        controllers = []
        for module in modules:
            for cls in Controller.children_classes.get(module, []):
                if is_valid(cls):
                    controllers.append(cls())
        return controllers

    for ctrl in build_controllers():
        for method_name, method in inspect.getmembers(ctrl, inspect.ismethod):

            # Skip this method if it is not @route decorated anywhere in
            # the hierarchy
            def is_method_a_route(cls):
                return getattr(getattr(cls, method_name, None), 'original_routing', None) is not None
            if not any(map(is_method_a_route, type(ctrl).mro())):
                continue

            merged_routing = {
                # 'type': 'http',  # set below
                'auth': 'user',
                'methods': None,
                'routes': [],
            }

            for cls in unique(reversed(type(ctrl).mro()[:-2])):  # ancestors first
                if method_name not in cls.__dict__:
                    continue
                submethod = getattr(cls, method_name)

                if not hasattr(submethod, 'original_routing'):
                    _logger.warning("The endpoint %s is not decorated by @route(), decorating it myself.", f'{cls.__module__}.{cls.__name__}.{method_name}')
                    submethod = route()(submethod)

                _check_and_complete_route_definition(cls, submethod, merged_routing)

                merged_routing.update(submethod.original_routing)

            if not merged_routing['routes']:
                _logger.warning("%s is a controller endpoint without any route, skipping.", f'{cls.__module__}.{cls.__name__}.{method_name}')
                continue

            if nodb_only and merged_routing['auth'] != "none":
                continue

            for url in merged_routing['routes']:
                # duplicates the function (partial) with a copy of the
                # original __dict__ (update_wrapper) to keep a reference
                # to `original_routing` and `original_endpoint`, assign
                # the merged routing ONLY on the duplicated function to
                # ensure method's immutability.
                endpoint = functools.partial(method)
                functools.update_wrapper(endpoint, method)
                endpoint.routing = merged_routing

                yield (url, endpoint)


def _check_and_complete_route_definition(controller_cls, submethod, merged_routing):
    """Verify and complete the route definition.

    * Ensure 'type' is defined on each method's own routing.
    * Ensure overrides don't change the routing type or the read/write mode

    :param submethod: route method
    :param dict merged_routing: accumulated routing values
    """
    default_type = submethod.original_routing.get('type', 'http')
    routing_type = merged_routing.setdefault('type', default_type)
    if submethod.original_routing.get('type') not in (None, routing_type):
        _logger.warning(
            "The endpoint %s changes the route type, using the original type: %r.",
            f'{controller_cls.__module__}.{controller_cls.__name__}.{submethod.__name__}',
            routing_type)
    submethod.original_routing['type'] = routing_type