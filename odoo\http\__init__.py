# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Odoo HTTP layer / WSGI application

This module provides the HTTP layer for Odoo, including request/response handling,
routing, session management, and WSGI application functionality.

The module has been refactored into a modular structure for better maintainability:
- wsgi: WSGI application and static file serving
- request: HTTP request handling and processing
- response: HTTP response handling and QWeb support
- streaming: File streaming and binary field handling
- dispatchers: Request dispatching and routing
- session: Session management and storage
- utils: Utility functions, constants, and helpers
- routing: Route decorators and routing utilities
- exceptions: HTTP-specific exceptions

For backward compatibility, all public APIs are re-exported from this module.
"""

# Import all public APIs for backward compatibility
from .wsgi import Application, root
from .request import Request, HTTPRequest, request, borrow_request
from .response import Response, Headers, ResponseCacheControl, ResponseStream, abort
from .streaming import Stream
from .dispatchers import Dispatcher, HttpDispatcher, JsonRPCDispatcher
from .session import Session, FilesystemSessionStore
from .exceptions import SessionExpiredException
from .routing import Controller, route
from .utils import (
    # Constants
    CORS_MAX_AGE, CSRF_FREE_METHODS, CSRF_TOKEN_SALT, DEFAULT_LANG,
    DEFAULT_MAX_CONTENT_LENGTH, JSON_MIMETYPES, ROUTING_KEYS,
    SESSION_LIFETIME, STATIC_CACHE, STATIC_CACHE_LONG,
    
    # Utility functions
    content_disposition, db_list, db_filter, dispatch_rpc,
    get_session_max_inactivity, is_cors_preflight, serialize_exception,
    get_default_session,
    
    # Classes
    GeoIP, FutureResponse
)
from .exceptions import RegistryError

# Re-export commonly used werkzeug exceptions for convenience
from werkzeug.exceptions import (
    BadRequest, Unauthorized, Forbidden, NotFound, MethodNotAllowed,
    InternalServerError, HTTPException
)

__all__ = [
    # Core classes
    'Application', 'Request', 'HTTPRequest', 'Response', 'Stream',
    'Session', 'FilesystemSessionStore', 'Controller',
    
    # Dispatchers
    'Dispatcher', 'HttpDispatcher', 'JsonRPCDispatcher',
    
    # Response utilities
    'Headers', 'ResponseCacheControl', 'ResponseStream',
    
    # Decorators and routing
    'route',
    
    # Utility functions
    'content_disposition', 'db_list', 'db_filter', 'dispatch_rpc',
    'get_session_max_inactivity', 'is_cors_preflight', 'serialize_exception',
    'get_default_session', 'abort', 'borrow_request',
    
    # Constants
    'CORS_MAX_AGE', 'CSRF_FREE_METHODS', 'CSRF_TOKEN_SALT', 'DEFAULT_LANG',
    'DEFAULT_MAX_CONTENT_LENGTH', 'JSON_MIMETYPES', 'ROUTING_KEYS',
    'SESSION_LIFETIME', 'STATIC_CACHE', 'STATIC_CACHE_LONG',
    
    # Utility classes
    'GeoIP', 'FutureResponse',
    
    # Exceptions
    'RegistryError', 'SessionExpiredException',
    
    # Global objects
    'request', 'root',
    
    # Werkzeug exceptions
    'BadRequest', 'Unauthorized', 'Forbidden', 'NotFound', 'MethodNotAllowed',
    'InternalServerError', 'HTTPException',
]
