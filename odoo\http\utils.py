# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Utility functions, constants, and helper classes for Odoo HTTP layer.

This module contains various utility functions, constants, decorators,
and helper classes used throughout the HTTP layer.
"""

import collections.abc
import logging
import re
import threading
import traceback
from urllib.parse import quote as url_quote

try:
    import geoip2.database
    import geoip2.errors
    import geoip2.models
    import maxminddb
except ImportError:
    geoip2 = None
    maxminddb = None

import psycopg2
import werkzeug.datastructures

import odoo
import odoo.service.common
import odoo.service.db
import odoo.service.model
from odoo.tools import config, exception_to_unicode
from odoo.tools.func import lazy_property

_logger = logging.getLogger(__name__)

# =========================================================
# Constants
# =========================================================

# The validity duration of a preflight response, one day.
CORS_MAX_AGE = 60 * 60 * 24

# The HTTP methods that do not require a CSRF validation.
CSRF_FREE_METHODS = ('GET', 'HEAD', 'OPTIONS', 'TRACE')

# The default csrf token lifetime, a salt against BREACH, one year
CSRF_TOKEN_SALT = 60 * 60 * 24 * 365

# The default lang to use when the browser doesn't specify it
DEFAULT_LANG = 'en_US'

DEFAULT_MAX_CONTENT_LENGTH = 128 * 1024 * 1024  # 128MiB

# The default duration of a user session cookie. Inactive sessions are reaped
# server-side as well with a threshold that can be set via an optional
# config parameter `sessions.max_inactivity_seconds` (default: SESSION_LIFETIME)
SESSION_LIFETIME = 60 * 60 * 24 * 7

# The cache duration for static content from the filesystem, one week.
STATIC_CACHE = 60 * 60 * 24 * 7

# The cache duration for content where the url uniquely identifies the
# content (usually using a hash), one year.
STATIC_CACHE_LONG = 60 * 60 * 24 * 365

# JSON mimetypes for request content type detection
JSON_MIMETYPES = ('application/json', 'application/json-rpc')

# Routing keys for endpoint configuration
ROUTING_KEYS = frozenset([
    'auth', 'cors', 'csrf', 'max_content_length', 'methods', 'readonly',
    'save_session', 'type', 'website'
])

# Warning message for missing CSRF token
MISSING_CSRF_WARNING = "CSRF validation failed on path '%s'. You may need to add csrf_token to your request parameters."

# Two empty objects used when the geolocalization failed. They have the
# sames attributes as real countries/cities except that accessing them
# evaluates to None.
if geoip2:
    GEOIP_EMPTY_COUNTRY = geoip2.models.Country({})
    GEOIP_EMPTY_CITY = geoip2.models.City({})
else:
    GEOIP_EMPTY_COUNTRY = None
    GEOIP_EMPTY_CITY = None


# =========================================================
# Utility Functions
# =========================================================

def get_default_session():
    """The dictionary to initialise a new session with."""
    return {
        'context': {},  # 'lang': request.default_lang()  # must be set at runtime
        'db': None,
        'debug': '',
        'login': None,
        'uid': None,
        'session_token': None,
        '_trace': [],
    }


def content_disposition(filename, disposition_type='attachment'):
    """
    Craft a ``Content-Disposition`` header, see :rfc:`6266`.

    :param filename: The name of the file, should that file be saved on
        disk by the browser.
    :param disposition_type: Tell the browser what to do with the file,
        either ``"attachment"`` to save the file on disk,
        either ``"inline"`` to display the file.
    """
    if disposition_type not in ('attachment', 'inline'):
        e = f"Invalid disposition_type: {disposition_type!r}"
        raise ValueError(e)
    return "{}; filename*=UTF-8''{}".format(
        disposition_type,
        url_quote(filename, safe='')  # RFC6266
    )


def db_list(force=False, host=None):
    """
    Get the list of available databases.

    :param bool force: See :func:`~odoo.service.db.list_dbs`:
    :param host: The Host used to replace %h and %d in the dbfilters
        regexp. Taken from the current request when omitted.
    :returns: the list of available databases
    :rtype: List[str]
    """
    try:
        dbs = odoo.service.db.list_dbs(force)
    except psycopg2.OperationalError:
        return []
    return db_filter(dbs, host)


def db_filter(dbs, host=None):
    """
    Return the subset of ``dbs`` that match the dbfilter or the dbname
    server configuration. In case neither are configured, return ``dbs``
    as-is.

    :param Iterable[str] dbs: The list of database names to filter.
    :param host: The Host used to replace %h and %d in the dbfilters
        regexp. Taken from the current request when omitted.
    :returns: The original list filtered.
    :rtype: List[str]
    """
    # Import here to avoid circular imports
    from .request import request
    
    if config['dbfilter']:
        #        host
        #     -----------
        # www.example.com:80
        #     -------
        #     domain
        if host is None:
            host = request.httprequest.environ.get('HTTP_HOST', '')
        host = host.partition(':')[0]
        if host.startswith('www.'):
            host = host[4:]
        domain = host.partition('.')[0]

        dbfilter_re = re.compile(
            config["dbfilter"].replace("%h", re.escape(host))
                              .replace("%d", re.escape(domain)))
        return [db for db in dbs if dbfilter_re.match(db)]

    if config['db_name']:
        # In case --db-filter is not provided and --database is passed, Odoo will
        # use the value of --database as a comma separated list of exposed databases.
        exposed_dbs = {db.strip() for db in config['db_name'].split(',')}
        return sorted(exposed_dbs.intersection(dbs))

    return list(dbs)


def dispatch_rpc(service_name, method, params):
    """
    Perform a RPC call.

    :param str service_name: either "common", "db" or "object".
    :param str method: the method name of the given service to execute
    :param Mapping params: the keyword arguments for method call
    :return: the return value of the called method
    :rtype: Any
    """
    # Import here to avoid circular imports
    from .request import borrow_request
    
    rpc_dispatchers = {
        'common': odoo.service.common.dispatch,
        'db': odoo.service.db.dispatch,
        'object': odoo.service.model.dispatch,
    }

    with borrow_request():
        threading.current_thread().uid = None
        threading.current_thread().dbname = None

        dispatch = rpc_dispatchers[service_name]
        return dispatch(method, params)


def get_session_max_inactivity(env):
    """Get the maximum session inactivity time from configuration."""
    if not env or env.cr._closed:
        return SESSION_LIFETIME

    ICP = env['ir.config_parameter'].sudo()

    try:
        return int(ICP.get_param('sessions.max_inactivity_seconds', SESSION_LIFETIME))
    except ValueError:
        _logger.warning("Invalid value for 'sessions.max_inactivity_seconds', using default value.")
        return SESSION_LIFETIME


def is_cors_preflight(request, endpoint):
    """Check if the request is a CORS preflight request."""
    return request.httprequest.method == 'OPTIONS' and endpoint.routing.get('cors', False)


def serialize_exception(exception):
    """Serialize an exception for JSON-RPC error responses."""
    name = type(exception).__name__
    module = type(exception).__module__

    return {
        'name': f'{module}.{name}' if module else name,
        'debug': traceback.format_exc(),
        'message': exception_to_unicode(exception),
        'arguments': exception.args,
        'context': getattr(exception, 'context', {}),
    }


# =========================================================
# Helper Classes
# =========================================================

class FutureResponse:
    """
    werkzeug.Response mock class that only serves as placeholder for
    headers to be injected in the final response.
    """
    # used by werkzeug.Response.set_cookie
    charset = 'utf-8'
    max_cookie_size = 4093

    def __init__(self):
        self.headers = werkzeug.datastructures.Headers()

    @property
    def _charset(self):
        return self.charset

    def set_cookie(self, *args, **kwargs):
        # Import here to avoid circular imports
        from .response import _Response
        _Response.set_cookie(self, *args, **kwargs)


class GeoIP(collections.abc.Mapping):
    """
    Ip Geolocalization utility, determine information such as the
    country or the timezone of the user based on their IP Address.

    The instances share the same API as `:class:`geoip2.models.City`
    <https://geoip2.readthedocs.io/en/latest/#geoip2.models.City>`_.

    When the IP couldn't be geolocalized (missing database, bad address)
    then an empty object is returned. This empty object can be used like
    a regular one with the exception that all info are set None.

    :param str ip: The IP Address to geo-localize

    .. note:

        The geoip info the the current request are available at
        :attr:`~odoo.http.request.geoip`.

    .. code-block:

        >>> GeoIP('127.0.0.1').country.iso_code
        >>> odoo_ip = socket.gethostbyname('odoo.com')
        >>> GeoIP(odoo_ip).country.iso_code
        'FR'
    """

    def __init__(self, ip):
        self.ip = ip

    @lazy_property
    def _city_record(self):
        # Import here to avoid circular imports
        from .wsgi import root
        try:
            return root.geoip_city_db.city(self.ip)
        except (OSError, maxminddb.InvalidDatabaseError):
            return GEOIP_EMPTY_CITY
        except geoip2.errors.AddressNotFoundError:
            return GEOIP_EMPTY_CITY

    @lazy_property
    def _country_record(self):
        if '_city_record' in vars(self):
            # the City class inherits from the Country class and the
            # city record is in cache already, save a geolocalization
            return self._city_record
        # Import here to avoid circular imports
        from .wsgi import root
        try:
            return root.geoip_country_db.country(self.ip)
        except (OSError, maxminddb.InvalidDatabaseError):
            return self._city_record
        except geoip2.errors.AddressNotFoundError:
            return GEOIP_EMPTY_COUNTRY

    @property
    def country_name(self):
        return self.country.name or self.continent.name

    @property
    def country_code(self):
        return self.country.iso_code or self.continent.code

    def __getattr__(self, attr):
        return getattr(self._country_record, attr)

    # Old dict API, undocumented for now, will be deprecated some day
    def __getitem__(self, item):
        if item == 'country_name':
            return self.country_name

        if item == 'country_code':
            return self.country_code

        if item == 'city':
            return self.city.name

        if item == 'latitude':
            return self.location.latitude

        if item == 'longitude':
            return self.location.longitude

        if item == 'region':
            return self.subdivisions[0].iso_code if self.subdivisions else None

        if item == 'time_zone':
            return self.location.time_zone

        raise KeyError(item)

    def __iter__(self):
        raise NotImplementedError("The dictionnary GeoIP API is deprecated.")

    def __len__(self):
        raise NotImplementedError("The dictionnary GeoIP API is deprecated.")
